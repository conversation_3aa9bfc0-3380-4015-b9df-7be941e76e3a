// 抢单大厅自动抢单脚本
// 基于贝塞尔曲线滑动和智能订单筛选

// 配置存储
var storage = storages.create('hall_order_config');

// 贝塞尔曲线滑动函数
function getBezierPath(p1, p2, p3, p4, times) {
  function Point2D(x, y) {
    this.x = x || 0.0;
    this.y = y || 0.0;
  }
  function PointOnCubicBezier(cp, t) {
    var ax, bx, cx;
    var ay, by, cy;
    var tSquared, tCubed;
    var result = new Point2D();
    cx = 3.0 * (cp[1].x - cp[0].x);
    bx = 3.0 * (cp[2].x - cp[1].x) - cx;
    ax = cp[3].x - cp[0].x - cx - bx;
    cy = 3.0 * (cp[1].y - cp[0].y);
    by = 3.0 * (cp[2].y - cp[1].y) - cy;
    ay = cp[3].y - cp[0].y - cy - by;
    tSquared = t * t;
    tCubed = tSquared * t;
    result.x = ax * tCubed + bx * tSquared + cx * t + cp[0].x;
    result.y = ay * tCubed + by * tSquared + cy * t + cp[0].y;
    return result;
  }
  function ComputeBezier(cp, numberOfPoints, curve) {
    var dt;
    var i;
    dt = 1.0 / (numberOfPoints - 1);
    for (i = 0; i < numberOfPoints; i++) {
      curve[i] = PointOnCubicBezier(cp, i * dt);
    }
  }
  var cp = [
    new Point2D(parseInt(p4[0]), parseInt(p4[1])),
    new Point2D(p2[0], p2[1]),
    new Point2D(p3[0], p3[1]),
    new Point2D(p1[0], p1[1]),
  ];
  var numberOfPoints = times;
  var curve = [];
  ComputeBezier(cp, numberOfPoints, curve);
  return curve;
}

// 从下往上滑动（下拉刷新）
function swipeTop(dw, dh, duration) {
  if (!(typeof duration === 'number' && !isNaN(duration) && duration > 0)) {
    duration = 400;
  }
  let grid = Math.round(dw * 0.1);
  let xStart = Math.round(dw * 0.3);
  let xEnd = Math.round(dw * 0.7);
  let yStart = Math.round(dh * 0.25);
  let yEnd = Math.round(dh * 0.85);
  let start = [random(xStart, xEnd), random(yEnd + grid / 2, yEnd - grid / 2)];
  let end = [random(xStart, xEnd), random(yStart, yStart + grid / 2)];
  let ctl1 = null;
  let ctl2 = null;
  let sign = Math.round(Math.random());
  if (sign === 0) {
    ctl1 = [end[0] - random(grid, grid * 2), end[1] - random(0, grid)];
    ctl2 = [end[0] + random(grid, grid * 2), end[1] + random(0, grid)];
  } else {
    ctl1 = [end[0] + random(grid, grid * 2), end[1] + random(0, grid)];
    ctl2 = [end[0] - random(grid, grid * 2), end[1] - random(0, grid)];
  }
  let path = getBezierPath(end, ctl1, ctl2, start, 100);
  let parms = [duration];
  for (let i = 0; i < path.length; i++) {
    let { x, y } = path[i];
    parms.push([x, y]);
  }
  gesture.apply(null, parms);
}

// 默认配置
function getDefaultConfig() {
    return {
        hallOrderEnabled: true,
        orderTypes: ['快车', '特惠快车'],
        amountEnabled: false,
        minAmount: 0,
        maxAmount: 999,
        startDistanceEnabled: false,
        maxStartDistance: 10,
        totalDistanceEnabled: false,
        minTotalDistance: 5,
        unitPriceEnabled: false,
        minUnitPrice: 2.0,
        bigOrderEnabled: false,
        bigOrderAmount: 100,
        refreshIntervalMin: 500,
        refreshIntervalMax: 1000
    };
}

// 读取配置
function getConfig() {
    return storage.get('config', getDefaultConfig());
}

// 生成随机刷新间隔
function getRandomRefreshInterval(config) {
    var min = config.refreshIntervalMin;
    var max = config.refreshIntervalMax;
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 等待抢单大厅页面加载
function waitForHallPage() {
    console.log('等待抢单大厅页面加载...');
    var maxWaitTime = 30000; // 最大等待30秒
    var startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
        // 检查是否有"抢单大厅"标题
        var titleElement = textContains('抢单大厅').findOne(1000);
        if (titleElement) {
            console.log('抢单大厅页面已加载');
            sleep(2000); // 等待页面完全渲染
            return true;
        }
        sleep(1000);
    }
    
    console.log('等待抢单大厅页面超时');
    return false;
}

// 解析订单信息
function parseOrderInfo(orderElement) {
    try {
        var orderInfo = {
            type: '',
            distance: 0,
            amount: 0,
            totalDistance: 0,
            unitPrice: 0,
            element: orderElement
        };
        
        // 获取订单区域内的所有文本
        var textElements = orderElement.find(className('android.widget.TextView'));
        var allTexts = [];
        
        for (var i = 0; i < textElements.length; i++) {
            var text = textElements[i].text();
            if (text && text.trim()) {
                allTexts.push(text.trim());
            }
        }
        
        // 解析各种信息
        for (var j = 0; j < allTexts.length; j++) {
            var text = allTexts[j];
            
            // 解析距离信息：距您X.XXkm
            var distanceMatch = text.match(/距您(\d+\.?\d*)km/);
            if (distanceMatch) {
                orderInfo.distance = parseFloat(distanceMatch[1]);
            }
            
            // 解析收入信息：预估收入X.XX元 或 价格约X.X元
            var amountMatch = text.match(/(?:预估收入|价格约)(\d+\.?\d*)元/);
            if (amountMatch) {
                orderInfo.amount = parseFloat(amountMatch[1]);
            }
            
            // 解析全程距离：预计全程X.XXkm
            var totalDistanceMatch = text.match(/预计全程(\d+\.?\d*)km/);
            if (totalDistanceMatch) {
                orderInfo.totalDistance = parseFloat(totalDistanceMatch[1]);
            }
            
            // 解析订单类型
            if (text.includes('快车')) {
                orderInfo.type = '快车';
            } else if (text.includes('特惠快车')) {
                orderInfo.type = '特惠快车';
            } else if (text.includes('滴滴特快')) {
                orderInfo.type = '滴滴特快';
            } else if (text.includes('自选单')) {
                orderInfo.type = '自选单';
            } else if (text.includes('随心接')) {
                orderInfo.type = '随心接实时单';
            }
        }
        
        // 计算单价（如果有全程距离和金额）
        if (orderInfo.totalDistance > 0 && orderInfo.amount > 0) {
            orderInfo.unitPrice = orderInfo.amount / orderInfo.totalDistance;
        }
        
        return orderInfo;
    } catch (e) {
        console.log('解析订单信息失败：' + e.message);
        return null;
    }
}

// 检查订单是否符合条件
function checkOrderConditions(orderInfo, config) {
    try {
        // 检查大厅订单是否开启
        if (!config.hallOrderEnabled) {
            return false;
        }
        
        // 检查订单类型
        if (orderInfo.type && !config.orderTypes.includes(orderInfo.type)) {
            return false;
        }
        
        // 检查订单金额
        if (config.amountEnabled) {
            if (orderInfo.amount < config.minAmount || orderInfo.amount > config.maxAmount) {
                return false;
            }
        }
        
        // 检查起点距离
        if (config.startDistanceEnabled) {
            if (orderInfo.distance > config.maxStartDistance) {
                return false;
            }
        }
        
        // 检查全程距离
        if (config.totalDistanceEnabled) {
            if (orderInfo.totalDistance < config.minTotalDistance) {
                return false;
            }
        }
        
        // 检查单价
        if (config.unitPriceEnabled) {
            if (orderInfo.unitPrice < config.minUnitPrice) {
                return false;
            }
        }
        
        // 检查是否为大单必抢
        if (config.bigOrderEnabled) {
            if (orderInfo.amount >= config.bigOrderAmount) {
                console.log('发现大单，必须抢单：' + orderInfo.amount + '元');
                return true; // 大单必抢，直接返回true
            }
        }
        
        return true;
    } catch (e) {
        console.log('检查订单条件失败：' + e.message);
        return false;
    }
}

// 执行抢单操作
function grabOrder(orderInfo) {
    try {
        console.log('准备抢单：类型=' + orderInfo.type + ', 金额=' + orderInfo.amount + '元, 距离=' + orderInfo.distance + 'km');

        // 在订单元素中查找抢单按钮
        var grabButton = orderInfo.element.findOne(text('抢单'));
        if (!grabButton) {
            // 尝试其他可能的按钮文本
            grabButton = orderInfo.element.findOne(textContains('抢'));
        }

        if (grabButton && grabButton.clickable()) {
            // 添加随机延迟，模拟人类反应时间
            var reactionDelay = random(100, 500);
            sleep(reactionDelay);

            // 获取按钮中心点并添加随机偏移
            var bounds = grabButton.bounds();
            var centerX = bounds.centerX() + random(-10, 10);
            var centerY = bounds.centerY() + random(-5, 5);

            // 执行点击
            click(centerX, centerY);
            console.log('✅ 已点击抢单按钮');

            // 等待抢单结果
            sleep(1000);

            // 检查是否抢单成功（可以根据实际情况调整检测逻辑）
            if (textContains('抢单成功').exists() || textContains('订单已接').exists()) {
                console.log('🎉 抢单成功！');
                return true;
            } else if (textContains('抢单失败').exists() || textContains('订单已被').exists()) {
                console.log('❌ 抢单失败，订单已被其他司机接取');
                return false;
            } else {
                console.log('⏳ 抢单结果未知，继续监控');
                return false;
            }
        } else {
            console.log('❌ 未找到可点击的抢单按钮');
            return false;
        }
    } catch (e) {
        console.log('抢单操作失败：' + e.message);
        return false;
    }
}

// 获取当前页面的所有订单
function getAllOrders() {
    try {
        var orders = [];

        // 查找所有可能的订单容器
        // 根据demo.txt分析，订单通常在特定的View容器中
        var orderContainers = className('android.view.View').find();

        for (var i = 0; i < orderContainers.length; i++) {
            var container = orderContainers[i];

            // 检查容器是否包含订单相关信息
            var hasOrderInfo = container.findOne(textMatches(/距您\d+\.?\d*km/)) ||
                              container.findOne(textMatches(/预估收入\d+\.?\d*元/)) ||
                              container.findOne(textMatches(/价格约\d+\.?\d*元/));

            if (hasOrderInfo) {
                var orderInfo = parseOrderInfo(container);
                if (orderInfo && orderInfo.amount > 0) {
                    orders.push(orderInfo);
                }
            }
        }

        console.log('发现 ' + orders.length + ' 个订单');
        return orders;
    } catch (e) {
        console.log('获取订单列表失败：' + e.message);
        return [];
    }
}

// 刷新订单列表
function refreshOrders() {
    try {
        console.log('正在刷新订单列表...');

        // 使用贝塞尔曲线下拉刷新
        swipeTop(device.width, device.height, random(300, 600));

        // 等待刷新完成
        sleep(random(1500, 2500));

        console.log('订单列表刷新完成');
        return true;
    } catch (e) {
        console.log('刷新订单失败：' + e.message);
        return false;
    }
}

// 统计信息
var stats = {
    totalOrders: 0,
    matchedOrders: 0,
    grabAttempts: 0,
    grabSuccess: 0,
    startTime: Date.now()
};

// 打印统计信息
function printStats() {
    var runTime = Math.floor((Date.now() - stats.startTime) / 1000);
    var minutes = Math.floor(runTime / 60);
    var seconds = runTime % 60;

    console.log('=== 运行统计 ===');
    console.log('运行时间: ' + minutes + '分' + seconds + '秒');
    console.log('总订单数: ' + stats.totalOrders);
    console.log('符合条件: ' + stats.matchedOrders);
    console.log('抢单尝试: ' + stats.grabAttempts);
    console.log('抢单成功: ' + stats.grabSuccess);
    if (stats.grabAttempts > 0) {
        console.log('成功率: ' + Math.round(stats.grabSuccess / stats.grabAttempts * 100) + '%');
    }
    console.log('===============');
}

// 主抢单循环
function mainGrabLoop() {
    var config = getConfig();
    console.log('当前配置：');
    console.log(JSON.stringify(config, null, 2));

    var loopCount = 0;

    while (true) {
        try {
            loopCount++;
            console.log('\n--- 第 ' + loopCount + ' 轮抢单 ---');

            // 刷新订单列表
            if (!refreshOrders()) {
                console.log('刷新失败，等待后重试');
                sleep(3000);
                continue;
            }

            // 获取所有订单
            var orders = getAllOrders();
            stats.totalOrders += orders.length;

            if (orders.length === 0) {
                console.log('暂无订单，继续监控...');
            } else {
                // 处理每个订单
                for (var i = 0; i < orders.length; i++) {
                    var order = orders[i];

                    console.log('检查订单 ' + (i + 1) + ': 类型=' + order.type +
                               ', 金额=' + order.amount + '元, 距离=' + order.distance + 'km');

                    // 检查是否符合条件
                    if (checkOrderConditions(order, config)) {
                        stats.matchedOrders++;
                        stats.grabAttempts++;

                        console.log('✅ 订单符合条件，开始抢单');

                        if (grabOrder(order)) {
                            stats.grabSuccess++;
                            console.log('🎉 抢单成功！暂停5秒...');
                            sleep(5000);
                        }

                        // 抢单后短暂休息
                        sleep(random(500, 1500));
                    } else {
                        console.log('❌ 订单不符合条件，跳过');
                    }
                }
            }

            // 每10轮打印一次统计
            if (loopCount % 10 === 0) {
                printStats();
            }

            // 等待下次刷新
            var interval = getRandomRefreshInterval(config);
            console.log('等待 ' + interval + ' 毫秒后继续...');
            sleep(interval);

        } catch (e) {
            console.log('主循环异常：' + e.message);
            console.log('等待3秒后继续...');
            sleep(3000);
        }
    }
}

// 主函数
function main() {
    console.log('=== 抢单大厅自动抢单脚本启动 ===');

    // 检查无障碍权限
    if (!auto.service) {
        console.log('请先开启无障碍服务');
        auto.waitFor();
    }

    // 等待抢单大厅页面
    if (!waitForHallPage()) {
        console.log('未检测到抢单大厅页面，请手动进入后重新运行脚本');
        return;
    }

    console.log('开始自动抢单...');

    // 启动主循环
    mainGrabLoop();
}

// 异常处理
try {
    main();
} catch (e) {
    console.log('脚本运行异常：' + e.message);
    printStats();
}
