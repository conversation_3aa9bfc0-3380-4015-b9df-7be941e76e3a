"ui";

var color = "#FF5722"; // 橙色主题

ui.layout(
    <vertical bg="#f5f5f5">
        <appbar bg="#FF5722">
            <toolbar id="toolbar" title="烈克" titleTextColor="white" gravity="center"/>
        </appbar>

        <ScrollView>
            <vertical padding="16">
                <card cardCornerRadius="8dp" cardElevation="4dp" margin="8" cardBackgroundColor="white">
                    <vertical padding="16">
                        <horizontal gravity="center_vertical" marginBottom="16">
                            <text text="刷新速度:" textSize="16sp" textColor="#333" layout_weight="1"/>
                            <horizontal>
                                <input id="refreshMinInput" text="800" inputType="number"
                                       textSize="14sp" w="60dp" gravity="center"/>
                                <text text="ms" textSize="14sp" textColor="#666" marginLeft="4dp" marginRight="16dp"/>
                                <input id="refreshMaxInput" text="1000" inputType="number"
                                       textSize="14sp" w="60dp" gravity="center"/>
                                <text text="ms" textSize="14sp" textColor="#666" marginLeft="4dp"/>
                            </horizontal>
                        </horizontal>

                        <horizontal gravity="center_vertical" marginBottom="16">
                            <text text="不接位置:" textSize="16sp" textColor="#333" marginRight="16dp"/>
                            <input id="excludeLocationInput" text="火车站#机场" textSize="14sp" layout_weight="1"/>
                        </horizontal>

                        <horizontal gravity="center_vertical">
                            <text text="软件卡密:" textSize="16sp" textColor="#333" marginRight="16dp"/>
                            <input id="licenseKeyInput" text="9d9a3f3969069522" textSize="14sp" layout_weight="1"/>
                        </horizontal>
                        <text text="到期时间 2025-08-26 19:46" textSize="12sp" textColor="#999" gravity="right" marginTop="8"/>
                    </vertical>
                </card>

                <card cardCornerRadius="8dp" cardElevation="4dp" margin="8" cardBackgroundColor="white">
                    <vertical padding="16">
                        <horizontal gravity="center_vertical" marginBottom="16">
                            <horizontal gravity="center_vertical" layout_weight="1">
                                <img id="realtimeIcon" src="./icons/realtime.png" w="24dp" h="24dp" marginRight="8dp"/>
                                <text text="实时单" textSize="18sp" textColor="#333"/>
                            </horizontal>
                            <Switch id="realtimeOrderSwitch" checked="true"/>
                        </horizontal>

                        <horizontal gravity="center_vertical" marginBottom="12">
                            <text text="价格范围:" textSize="16sp" textColor="#333" marginRight="16dp"/>
                            <input id="realtimeMinAmountInput" text="10" inputType="numberDecimal"
                                   textSize="14sp" w="50dp" gravity="center"/>
                            <text text="元" textSize="14sp" textColor="#666" marginLeft="4dp" marginRight="16dp"/>
                            <input id="realtimeMaxAmountInput" text="999" inputType="numberDecimal"
                                   textSize="14sp" w="50dp" gravity="center"/>
                            <text text="元" textSize="14sp" textColor="#666" marginLeft="4dp"/>
                        </horizontal>

                        <horizontal gravity="center_vertical" marginBottom="12">
                            <text text="起点距离:" textSize="16sp" textColor="#333" marginRight="16dp"/>
                            <text text="≤" textSize="16sp" textColor="#666" marginRight="8dp"/>
                            <input id="realtimeMaxDistanceInput" text="10" inputType="numberDecimal"
                                   textSize="14sp" w="50dp" gravity="center"/>
                            <text text="km" textSize="14sp" textColor="#666" marginLeft="4dp"/>
                        </horizontal>

                        <horizontal gravity="center_vertical">
                            <text text="公里单价:" textSize="16sp" textColor="#333" marginRight="16dp"/>
                            <text text="≥" textSize="16sp" textColor="#666" marginRight="8dp"/>
                            <input id="realtimeMinUnitPriceInput" text="0.8" inputType="numberDecimal"
                                   textSize="14sp" w="50dp" gravity="center"/>
                            <text text="元" textSize="14sp" textColor="#666" marginLeft="4dp"/>
                        </horizontal>
                    </vertical>
                </card>

                <card cardCornerRadius="8dp" cardElevation="4dp" margin="8" cardBackgroundColor="white">
                    <vertical padding="16">
                        <horizontal gravity="center_vertical" marginBottom="16">
                            <horizontal gravity="center_vertical" layout_weight="1">
                                <img id="reservationIcon" src="./icons/reservation.png" w="24dp" h="24dp" marginRight="8dp"/>
                                <text text="预约单" textSize="18sp" textColor="#333"/>
                            </horizontal>
                            <Switch id="reservationOrderSwitch" checked="true"/>
                        </horizontal>

                        <horizontal gravity="center_vertical" marginBottom="12">
                            <text text="价格范围:" textSize="16sp" textColor="#333" marginRight="16dp"/>
                            <input id="reservationMinAmountInput" text="50" inputType="numberDecimal"
                                   textSize="14sp" w="50dp" gravity="center"/>
                            <text text="元" textSize="14sp" textColor="#666" marginLeft="4dp" marginRight="16dp"/>
                            <input id="reservationMaxAmountInput" text="999" inputType="numberDecimal"
                                   textSize="14sp" w="50dp" gravity="center"/>
                            <text text="元" textSize="14sp" textColor="#666" marginLeft="4dp"/>
                        </horizontal>

                        <horizontal gravity="center_vertical" marginBottom="12">
                            <text text="起点距离:" textSize="16sp" textColor="#333" marginRight="16dp"/>
                            <text text="≤" textSize="16sp" textColor="#666" marginRight="8dp"/>
                            <input id="reservationMaxDistanceInput" text="20" inputType="numberDecimal"
                                   textSize="14sp" w="50dp" gravity="center"/>
                            <text text="km" textSize="14sp" textColor="#666" marginLeft="4dp"/>
                        </horizontal>

                        <horizontal gravity="center_vertical">
                            <text text="公里单价:" textSize="16sp" textColor="#333" marginRight="16dp"/>
                            <text text="≥" textSize="16sp" textColor="#666" marginRight="8dp"/>
                            <input id="reservationMinUnitPriceInput" text="0.8" inputType="numberDecimal"
                                   textSize="14sp" w="50dp" gravity="center"/>
                            <text text="元" textSize="14sp" textColor="#666" marginLeft="4dp"/>
                        </horizontal>
                    </vertical>
                </card>

                <button id="startBtn" text="开始运行" textColor="white" bg="#FF5722"
                        margin="16" textSize="18sp" h="50dp" style="@style/Widget.AppCompat.Button"/>
            </vertical>
        </ScrollView>
    </vertical>
);

// 配置存储
var storage = storages.create('didi_grabber_config');

// 默认配置
function getDefaultConfig() {
    return {
        // 刷新间隔
        refreshIntervalMin: 800,
        refreshIntervalMax: 1000,

        // 实时单配置
        realtimeOrderEnabled: true,
        realtimeMinAmount: 10,
        realtimeMaxAmount: 999,
        realtimeMaxDistance: 10,
        realtimeMinUnitPrice: 0.8,

        // 预约单配置
        reservationOrderEnabled: true,
        reservationMinAmount: 50,
        reservationMaxAmount: 999,
        reservationMaxDistance: 20,
        reservationMinUnitPrice: 0.8
    };
}

// 加载配置
function loadConfig() {
    var config = storage.get('config', getDefaultConfig());

    // 设置刷新间隔
    ui.refreshMinInput.setText(config.refreshIntervalMin.toString());
    ui.refreshMaxInput.setText(config.refreshIntervalMax.toString());

    // 设置实时单配置
    ui.realtimeOrderSwitch.setChecked(config.realtimeOrderEnabled);
    ui.realtimeMinAmountInput.setText(config.realtimeMinAmount.toString());
    ui.realtimeMaxAmountInput.setText(config.realtimeMaxAmount.toString());
    ui.realtimeMaxDistanceInput.setText(config.realtimeMaxDistance.toString());
    ui.realtimeMinUnitPriceInput.setText(config.realtimeMinUnitPrice.toString());

    // 设置预约单配置
    ui.reservationOrderSwitch.setChecked(config.reservationOrderEnabled);
    ui.reservationMinAmountInput.setText(config.reservationMinAmount.toString());
    ui.reservationMaxAmountInput.setText(config.reservationMaxAmount.toString());
    ui.reservationMaxDistanceInput.setText(config.reservationMaxDistance.toString());
    ui.reservationMinUnitPriceInput.setText(config.reservationMinUnitPrice.toString());

    return config;
}

// 保存配置
function saveConfig() {
    try {
        var config = {
            // 刷新间隔
            refreshIntervalMin: parseInt(ui.refreshMinInput.getText()) || 800,
            refreshIntervalMax: parseInt(ui.refreshMaxInput.getText()) || 1000,

            // 实时单配置
            realtimeOrderEnabled: ui.realtimeOrderSwitch.isChecked(),
            realtimeMinAmount: parseFloat(ui.realtimeMinAmountInput.getText()) || 10,
            realtimeMaxAmount: parseFloat(ui.realtimeMaxAmountInput.getText()) || 999,
            realtimeMaxDistance: parseFloat(ui.realtimeMaxDistanceInput.getText()) || 10,
            realtimeMinUnitPrice: parseFloat(ui.realtimeMinUnitPriceInput.getText()) || 0.8,

            // 预约单配置
            reservationOrderEnabled: ui.reservationOrderSwitch.isChecked(),
            reservationMinAmount: parseFloat(ui.reservationMinAmountInput.getText()) || 50,
            reservationMaxAmount: parseFloat(ui.reservationMaxAmountInput.getText()) || 999,
            reservationMaxDistance: parseFloat(ui.reservationMaxDistanceInput.getText()) || 20,
            reservationMinUnitPrice: parseFloat(ui.reservationMinUnitPriceInput.getText()) || 0.8
        };

        // 验证配置
        if (!validateConfig(config)) {
            return false;
        }

        storage.put('config', config);
        toast('配置保存成功！');
        return true;
    } catch (e) {
        toast('保存配置失败：' + e.message);
        return false;
    }
}

// 验证配置
function validateConfig(config) {
    // 验证刷新间隔
    if (config.refreshIntervalMin <= 0 || config.refreshIntervalMax <= 0) {
        toast('刷新间隔必须大于0');
        return false;
    }
    if (config.refreshIntervalMin > config.refreshIntervalMax) {
        toast('刷新间隔最小值不能大于最大值');
        return false;
    }

    // 验证实时单配置
    if (config.realtimeOrderEnabled) {
        if (config.realtimeMinAmount < 0 || config.realtimeMaxAmount < 0) {
            toast('实时单金额不能为负数');
            return false;
        }
        if (config.realtimeMinAmount > config.realtimeMaxAmount) {
            toast('实时单最低金额不能大于最高金额');
            return false;
        }
        if (config.realtimeMaxDistance <= 0) {
            toast('实时单起点距离必须大于0');
            return false;
        }
        if (config.realtimeMinUnitPrice <= 0) {
            toast('实时单公里单价必须大于0');
            return false;
        }
    }

    // 验证预约单配置
    if (config.reservationOrderEnabled) {
        if (config.reservationMinAmount < 0 || config.reservationMaxAmount < 0) {
            toast('预约单金额不能为负数');
            return false;
        }
        if (config.reservationMinAmount > config.reservationMaxAmount) {
            toast('预约单最低金额不能大于最高金额');
            return false;
        }
        if (config.reservationMaxDistance <= 0) {
            toast('预约单起点距离必须大于0');
            return false;
        }
        if (config.reservationMinUnitPrice <= 0) {
            toast('预约单公里单价必须大于0');
            return false;
        }
    }

    // 验证至少启用一种订单类型
    if (!config.realtimeOrderEnabled && !config.reservationOrderEnabled) {
        toast('请至少启用一种订单类型');
        return false;
    }

    return true;
}

// 读取配置用于业务处理
function getConfig() {
    return storage.get('config', getDefaultConfig());
}

// 事件绑定
ui.run(function() {
    // 加载配置
    loadConfig();

    // 开始运行按钮点击事件
    ui.startBtn.on('click', function() {
        if (saveConfig()) {
            toast('开始运行抢单程序...');
            // 这里可以添加启动抢单逻辑
        }
    });
});

//创建选项菜单(右上角)
ui.emitter.on("create_options_menu", menu=>{
    menu.add("关于");
});

//监听选项菜单点击
ui.emitter.on("options_item_selected", (e, item)=>{
    switch(item.getTitle()){
        case "关于":
            alert("关于", "烈克抢单助手 v1.0.0");
            break;
    }
    e.consumed = true;
});

activity.setSupportActionBar(ui.toolbar);
