# 图标使用说明

## 当前图标设置

代码中已经预留了图标位置，使用以下路径：
- 实时单图标：`./icons/realtime.png`
- 预约单图标：`./icons/reservation.png`

## 图标文件要求

### 文件位置
请在项目根目录下创建 `icons` 文件夹，并放入以下文件：
- `realtime.png` - 实时单图标
- `reservation.png` - 预约单图标

### 图标规格
- **尺寸**：24x24dp (建议72x72像素 @3x)
- **格式**：PNG格式，支持透明背景
- **颜色**：建议使用橙色主题 (#FF5722) 或深灰色 (#333333)
- **风格**：简洁的线条图标或填充图标

### 设计建议
- **实时单图标**：可以使用时钟、闪电、即时等相关元素
- **预约单图标**：可以使用日历、预约、计划等相关元素
- 保持图标风格统一，线条粗细一致
- 确保在小尺寸下清晰可见

## 如果暂时没有图标

如果您暂时没有准备图标文件，可以：

1. **创建空的icons文件夹**，程序会优雅地处理图标加载失败
2. **使用文字替代**：将图标代码改为文字
3. **隐藏图标**：将图标的 `visibility` 设为 `gone`

## 文件夹结构
```
didi_grabber/
├── ui.js
├── icons/
│   ├── realtime.png
│   └── reservation.png
└── 图标说明.md
```
