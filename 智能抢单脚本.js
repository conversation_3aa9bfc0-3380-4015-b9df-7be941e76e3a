// 智能抢单脚本 - 增强版
// 包含更多智能功能和优化

// 配置存储
var storage = storages.create('hall_order_config');

// 贝塞尔曲线滑动函数（复用）
function getBezierPath(p1, p2, p3, p4, times) {
  function Point2D(x, y) { this.x = x || 0.0; this.y = y || 0.0; }
  function PointOnCubicBezier(cp, t) {
    var ax, bx, cx, ay, by, cy, tSquared, tCubed;
    var result = new Point2D();
    cx = 3.0 * (cp[1].x - cp[0].x); bx = 3.0 * (cp[2].x - cp[1].x) - cx; ax = cp[3].x - cp[0].x - cx - bx;
    cy = 3.0 * (cp[1].y - cp[0].y); by = 3.0 * (cp[2].y - cp[1].y) - cy; ay = cp[3].y - cp[0].y - cy - by;
    tSquared = t * t; tCubed = tSquared * t;
    result.x = ax * tCubed + bx * tSquared + cx * t + cp[0].x;
    result.y = ay * tCubed + by * tSquared + cy * t + cp[0].y;
    return result;
  }
  function ComputeBezier(cp, numberOfPoints, curve) {
    var dt = 1.0 / (numberOfPoints - 1);
    for (var i = 0; i < numberOfPoints; i++) { curve[i] = PointOnCubicBezier(cp, i * dt); }
  }
  var cp = [new Point2D(parseInt(p4[0]), parseInt(p4[1])), new Point2D(p2[0], p2[1]), 
            new Point2D(p3[0], p3[1]), new Point2D(p1[0], p1[1])];
  var curve = []; ComputeBezier(cp, times, curve); return curve;
}

function swipeTop(dw, dh, duration) {
  if (!(typeof duration === 'number' && !isNaN(duration) && duration > 0)) duration = 400;
  let grid = Math.round(dw * 0.1), xStart = Math.round(dw * 0.3), xEnd = Math.round(dw * 0.7);
  let yStart = Math.round(dh * 0.25), yEnd = Math.round(dh * 0.85);
  let start = [random(xStart, xEnd), random(yEnd + grid / 2, yEnd - grid / 2)];
  let end = [random(xStart, xEnd), random(yStart, yStart + grid / 2)];
  let sign = Math.round(Math.random());
  let ctl1 = sign === 0 ? [end[0] - random(grid, grid * 2), end[1] - random(0, grid)] : 
                          [end[0] + random(grid, grid * 2), end[1] + random(0, grid)];
  let ctl2 = sign === 0 ? [end[0] + random(grid, grid * 2), end[1] + random(0, grid)] : 
                          [end[0] - random(grid, grid * 2), end[1] - random(0, grid)];
  let path = getBezierPath(end, ctl1, ctl2, start, 100);
  let parms = [duration]; for (let i = 0; i < path.length; i++) parms.push([path[i].x, path[i].y]);
  gesture.apply(null, parms);
}

// 智能配置管理
class ConfigManager {
    constructor() {
        this.config = this.loadConfig();
    }
    
    loadConfig() {
        return storage.get('config', {
            hallOrderEnabled: true, orderTypes: ['快车', '特惠快车'], amountEnabled: false,
            minAmount: 0, maxAmount: 999, startDistanceEnabled: false, maxStartDistance: 10,
            totalDistanceEnabled: false, minTotalDistance: 5, unitPriceEnabled: false, minUnitPrice: 2.0,
            bigOrderEnabled: false, bigOrderAmount: 100, refreshIntervalMin: 500, refreshIntervalMax: 1000
        });
    }
    
    getRandomInterval() {
        return Math.floor(Math.random() * (this.config.refreshIntervalMax - this.config.refreshIntervalMin + 1)) + this.config.refreshIntervalMin;
    }
}

// 页面检测器
class PageDetector {
    static waitForHallPage(timeout = 30000) {
        console.log('等待抢单大厅页面加载...');
        var startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            if (textContains('抢单大厅').findOne(1000)) {
                console.log('✅ 抢单大厅页面已加载'); sleep(2000); return true;
            }
            sleep(1000);
        }
        console.log('❌ 等待抢单大厅页面超时'); return false;
    }
    
    static isPageStable() {
        // 检查页面是否稳定（无加载动画等）
        return !textContains('加载中').exists() && !textContains('刷新中').exists();
    }
}

// 订单解析器
class OrderParser {
    static parseOrder(element) {
        try {
            var order = { type: '', distance: 0, amount: 0, totalDistance: 0, unitPrice: 0, element: element };
            var texts = this.getAllTexts(element);
            
            texts.forEach(text => {
                var distanceMatch = text.match(/距您(\d+\.?\d*)km/);
                if (distanceMatch) order.distance = parseFloat(distanceMatch[1]);
                
                var amountMatch = text.match(/(?:预估收入|价格约)(\d+\.?\d*)元/);
                if (amountMatch) order.amount = parseFloat(amountMatch[1]);
                
                var totalMatch = text.match(/预计全程(\d+\.?\d*)km/);
                if (totalMatch) order.totalDistance = parseFloat(totalMatch[1]);
                
                if (text.includes('特惠快车')) order.type = '特惠快车';
                else if (text.includes('快车')) order.type = '快车';
                else if (text.includes('滴滴特快')) order.type = '滴滴特快';
                else if (text.includes('自选单')) order.type = '自选单';
                else if (text.includes('随心接')) order.type = '随心接实时单';
            });
            
            if (order.totalDistance > 0 && order.amount > 0) {
                order.unitPrice = order.amount / order.totalDistance;
            }
            
            return order.amount > 0 ? order : null;
        } catch (e) {
            console.log('解析订单失败：' + e.message); return null;
        }
    }
    
    static getAllTexts(element) {
        var texts = [], textElements = element.find(className('android.widget.TextView'));
        for (var i = 0; i < textElements.length; i++) {
            var text = textElements[i].text();
            if (text && text.trim()) texts.push(text.trim());
        }
        return texts;
    }
    
    static getAllOrders() {
        try {
            var orders = [], containers = className('android.view.View').find();
            for (var i = 0; i < containers.length; i++) {
                var container = containers[i];
                var hasOrderInfo = container.findOne(textMatches(/距您\d+\.?\d*km/)) ||
                                  container.findOne(textMatches(/预估收入\d+\.?\d*元/)) ||
                                  container.findOne(textMatches(/价格约\d+\.?\d*元/));
                if (hasOrderInfo) {
                    var order = this.parseOrder(container);
                    if (order) orders.push(order);
                }
            }
            console.log('📋 发现 ' + orders.length + ' 个订单');
            return orders;
        } catch (e) {
            console.log('获取订单失败：' + e.message); return [];
        }
    }
}

// 条件检查器
class ConditionChecker {
    static checkOrder(order, config) {
        if (!config.hallOrderEnabled) return false;
        if (order.type && !config.orderTypes.includes(order.type)) return false;
        if (config.amountEnabled && (order.amount < config.minAmount || order.amount > config.maxAmount)) return false;
        if (config.startDistanceEnabled && order.distance > config.maxStartDistance) return false;
        if (config.totalDistanceEnabled && order.totalDistance < config.minTotalDistance) return false;
        if (config.unitPriceEnabled && order.unitPrice < config.minUnitPrice) return false;
        if (config.bigOrderEnabled && order.amount >= config.bigOrderAmount) {
            console.log('🎯 发现大单必抢：' + order.amount + '元'); return true;
        }
        return true;
    }
}

// 抢单执行器
class GrabExecutor {
    static grabOrder(order) {
        try {
            console.log('🚀 准备抢单：' + order.type + ' ' + order.amount + '元 ' + order.distance + 'km');
            
            var grabButton = order.element.findOne(text('抢单')) || order.element.findOne(textContains('抢'));
            if (!grabButton || !grabButton.clickable()) {
                console.log('❌ 未找到可点击的抢单按钮'); return false;
            }
            
            sleep(random(100, 500)); // 模拟反应时间
            var bounds = grabButton.bounds();
            click(bounds.centerX() + random(-10, 10), bounds.centerY() + random(-5, 5));
            console.log('✅ 已点击抢单按钮');
            
            sleep(1000);
            if (textContains('抢单成功').exists() || textContains('订单已接').exists()) {
                console.log('🎉 抢单成功！'); return true;
            } else if (textContains('抢单失败').exists() || textContains('订单已被').exists()) {
                console.log('❌ 抢单失败'); return false;
            }
            console.log('⏳ 抢单结果未知'); return false;
        } catch (e) {
            console.log('抢单异常：' + e.message); return false;
        }
    }
}

// 统计管理器
class StatsManager {
    constructor() {
        this.stats = { totalOrders: 0, matchedOrders: 0, grabAttempts: 0, grabSuccess: 0, startTime: Date.now() };
    }
    
    addOrder() { this.stats.totalOrders++; }
    addMatch() { this.stats.matchedOrders++; }
    addAttempt() { this.stats.grabAttempts++; }
    addSuccess() { this.stats.grabSuccess++; }
    
    print() {
        var runTime = Math.floor((Date.now() - this.stats.startTime) / 1000);
        var minutes = Math.floor(runTime / 60), seconds = runTime % 60;
        console.log('=== 📊 运行统计 ===');
        console.log('⏰ 运行时间: ' + minutes + '分' + seconds + '秒');
        console.log('📋 总订单数: ' + this.stats.totalOrders);
        console.log('✅ 符合条件: ' + this.stats.matchedOrders);
        console.log('🚀 抢单尝试: ' + this.stats.grabAttempts);
        console.log('🎉 抢单成功: ' + this.stats.grabSuccess);
        if (this.stats.grabAttempts > 0) {
            console.log('📈 成功率: ' + Math.round(this.stats.grabSuccess / this.stats.grabAttempts * 100) + '%');
        }
        console.log('==================');
    }
}

// 主控制器
class GrabController {
    constructor() {
        this.configManager = new ConfigManager();
        this.statsManager = new StatsManager();
        this.isRunning = false;
    }
    
    async start() {
        console.log('🚀 智能抢单脚本启动');
        
        if (!auto.service) { console.log('请先开启无障碍服务'); auto.waitFor(); }
        if (!PageDetector.waitForHallPage()) {
            console.log('❌ 未检测到抢单大厅页面'); return;
        }
        
        this.isRunning = true;
        this.mainLoop();
    }
    
    mainLoop() {
        var loopCount = 0;
        while (this.isRunning) {
            try {
                loopCount++;
                console.log('\n🔄 第 ' + loopCount + ' 轮抢单');
                
                this.refreshOrders();
                var orders = OrderParser.getAllOrders();
                this.statsManager.stats.totalOrders += orders.length;
                
                if (orders.length === 0) {
                    console.log('📭 暂无订单');
                } else {
                    this.processOrders(orders);
                }
                
                if (loopCount % 10 === 0) this.statsManager.print();
                
                var interval = this.configManager.getRandomInterval();
                console.log('⏳ 等待 ' + interval + 'ms...');
                sleep(interval);
                
            } catch (e) {
                console.log('❌ 主循环异常：' + e.message);
                sleep(3000);
            }
        }
    }
    
    refreshOrders() {
        console.log('🔄 刷新订单列表...');
        swipeTop(device.width, device.height, random(300, 600));
        sleep(random(1500, 2500));
    }
    
    processOrders(orders) {
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            console.log('🔍 检查订单 ' + (i + 1) + ': ' + order.type + ' ' + order.amount + '元 ' + order.distance + 'km');
            
            if (ConditionChecker.checkOrder(order, this.configManager.config)) {
                this.statsManager.addMatch();
                this.statsManager.addAttempt();
                
                if (GrabExecutor.grabOrder(order)) {
                    this.statsManager.addSuccess();
                    sleep(5000); // 成功后休息
                }
                sleep(random(500, 1500));
            } else {
                console.log('❌ 不符合条件');
            }
        }
    }
    
    stop() {
        this.isRunning = false;
        this.statsManager.print();
        console.log('🛑 抢单脚本已停止');
    }
}

// 启动脚本
try {
    var controller = new GrabController();
    controller.start();
} catch (e) {
    console.log('❌ 脚本异常：' + e.message);
}
