# 抢单大厅自动抢单脚本使用说明

## 功能概述

本脚本基于AutoJS6开发，实现了滴滴抢单大厅的自动抢单功能。通过智能识别订单信息、条件筛选和自动抢单，大大提高抢单效率。

## 主要特性

### 🎯 智能订单识别
- 自动解析订单类型（快车、特惠快车、滴滴特快、自选单等）
- 精确提取距离、金额、全程距离等关键信息
- 计算订单单价，支持单价筛选

### 🔄 仿真滑动刷新
- 基于贝塞尔曲线算法的仿真滑动
- 模拟真实手指滑动轨迹
- 随机化滑动参数，避免被检测

### ⚙️ 灵活配置系统
- 支持多种订单类型筛选
- 金额范围、距离限制、单价要求
- 大单必抢功能
- 可调节刷新间隔

### 📊 实时统计监控
- 订单数量统计
- 抢单成功率计算
- 运行时间记录
- 详细日志输出

## 文件说明

### 1. 抢单大厅脚本.js
**基础版抢单脚本**
- 完整的抢单功能实现
- 详细的注释说明
- 适合学习和自定义修改

### 2. 智能抢单脚本.js
**增强版抢单脚本**
- 面向对象设计，代码更简洁
- 更好的错误处理和稳定性
- 优化的性能和响应速度

### 3. 大厅订单界面.js
**配置界面**
- 图形化配置界面
- 实时保存设置
- 配置验证功能

## 使用步骤

### 1. 环境准备
```javascript
// 确保AutoJS6已安装并开启无障碍服务
if (!auto.service) {
    console.log('请先开启无障碍服务');
    auto.waitFor();
}
```

### 2. 配置设置
1. 运行 `大厅订单界面.js` 进行配置
2. 设置订单筛选条件
3. 保存配置

### 3. 启动抢单
1. 手动进入滴滴抢单大厅页面
2. 运行 `抢单大厅脚本.js` 或 `智能抢单脚本.js`
3. 脚本自动开始抢单

## 配置参数说明

### 基础设置
- **大厅订单开关**: 控制整个抢单功能
- **订单类型**: 选择要抢的订单类型
- **刷新间隔**: 设置刷新频率（毫秒）

### 筛选条件
- **订单金额**: 设置金额范围筛选
- **起点距离**: 限制起点距离
- **全程距离**: 设置最小全程距离
- **订单单价**: 设置最低单价要求
- **大单必抢**: 超过指定金额自动抢单

## 核心算法

### 贝塞尔曲线滑动
```javascript
// 生成自然的滑动轨迹
function getBezierPath(p1, p2, p3, p4, times) {
    // 四点贝塞尔曲线计算
    // 返回平滑的轨迹点数组
}
```

### 订单信息解析
```javascript
// 正则表达式匹配关键信息
var distanceMatch = text.match(/距您(\d+\.?\d*)km/);
var amountMatch = text.match(/(?:预估收入|价格约)(\d+\.?\d*)元/);
var totalDistanceMatch = text.match(/预计全程(\d+\.?\d*)km/);
```

### 条件筛选逻辑
```javascript
// 多重条件判断
function checkOrderConditions(order, config) {
    // 1. 检查订单类型
    // 2. 检查金额范围
    // 3. 检查距离限制
    // 4. 检查单价要求
    // 5. 大单必抢优先
}
```

## 安全特性

### 1. 随机化策略
- 随机滑动轨迹和速度
- 随机点击位置偏移
- 随机操作间隔时间

### 2. 仿真行为
- 模拟人类反应时间
- 自然的操作节奏
- 避免机械化操作

### 3. 异常处理
- 网络异常自动重试
- 页面变化适应性处理
- 脚本崩溃自动恢复

## 性能优化

### 1. 高效解析
- 批量处理订单信息
- 优化正则表达式匹配
- 减少不必要的UI查询

### 2. 内存管理
- 及时释放无用对象
- 避免内存泄漏
- 控制循环次数

### 3. 响应速度
- 快速订单识别
- 即时抢单响应
- 最小化延迟时间

## 常见问题

### Q: 脚本无法识别订单？
A: 检查页面是否完全加载，确认在抢单大厅页面

### Q: 抢单成功率低？
A: 调整筛选条件，降低要求或增加订单类型

### Q: 脚本运行不稳定？
A: 检查网络连接，适当增加等待时间

### Q: 如何提高抢单速度？
A: 减少刷新间隔，但要注意避免过于频繁

## 注意事项

1. **合规使用**: 请遵守平台规则，合理使用脚本
2. **网络环境**: 确保网络稳定，避免频繁断线
3. **设备性能**: 建议在性能较好的设备上运行
4. **监控运行**: 定期检查脚本运行状态
5. **备份配置**: 重要配置建议备份保存

## 技术支持

如遇到问题或需要功能定制，请：
1. 查看详细日志输出
2. 检查配置是否正确
3. 确认页面结构是否变化
4. 尝试重启脚本或设备

## 更新日志

### v1.0.0
- 基础抢单功能实现
- 贝塞尔曲线滑动算法
- 订单信息解析
- 配置系统

### v1.1.0
- 增强版智能脚本
- 面向对象重构
- 性能优化
- 错误处理改进

---

**免责声明**: 本脚本仅供学习和研究使用，使用者需自行承担使用风险，遵守相关法律法规。
