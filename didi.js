/**
 * Auto.js v6 滴滴抢单脚本 (v6.2 - 舒适配色)
 * 功能: 优化了悬浮日志窗口的配色和样式，使其更清晰、更舒适。
 */

// --- 用户配置 ---
const config = {
    maxDistanceToPickup: 3.5, // (公里) 接驾距离小于此数值
    minIncome: 12.0,          // (元) 预估收入大于此数值
    minTotalDistance: 0,      // (公里) 预计全程大于此数值 (0表示不限制)
    pickupKeywords: [],       // 接单地址关键词, e.g., ['天河', '白云'] (空数组表示不限制)
    dropoffKeywords: [],      // 送达地址关键词 (空数组表示不限制)
    refreshIntervalMin: 1500,    // (毫秒) 每轮抢单的【最小】间隔时间
    refreshIntervalMax: 3500,    // (毫秒) 每轮抢单的【最大】间隔时间
    refreshWaitTime: 1200,     // (毫秒) 下拉刷新后等待列表加载的时间
    postClickWaitTime: 2500    // (毫秒) 点击抢单后，等待结果弹窗出现的时间
};

const MAX_LOG_LINES = 7;
let logHistory = [];

let floatingWindow = floaty.window(
    <frame id="background" w="auto" h="auto" bg="#B01A2633" paddingTop="5" paddingBottom="5" cornerRadius="10dp">
        <vertical>
            <text id="title" text="--- 抢单日志 ---" textColor="#00BCD4" textSize="14sp" w="*" gravity="center" padding="2" textStyle="bold"/>
            <text id="log_text" textColor="#EAEAEA" textSize="13sp" marginLeft="10" marginRight="10" w="auto"/>
        </vertical>
    </frame>
);

floatingWindow.setPosition(0, device.height * 0.15);

let isDragging = false;
let lastTouchX, lastTouchY;
let windowX, windowY;

floatingWindow.title.setOnTouchListener(function(view, event) {
    switch (event.getAction()) {
        case event.ACTION_DOWN:
            lastTouchX = event.getRawX();
            lastTouchY = event.getRawY();
            windowX = floatingWindow.getX();
            windowY = floatingWindow.getY();
            isDragging = true;
            return true;
        case event.ACTION_MOVE:
            if (isDragging) {
                let dx = event.getRawX() - lastTouchX;
                let dy = event.getRawY() - lastTouchY;
                floatingWindow.setPosition(windowX + dx, windowY + dy);
            }
            return true;
        case event.ACTION_UP:
            isDragging = false;
            return true;
    }
    return false;
});

events.on('exit', function() {
    floaty.closeAll();
});

function showLog(message) {
    let timestamp = new java.text.SimpleDateFormat("HH:mm:ss").format(new java.util.Date());
    let formattedMessage = `[${timestamp}] ${message}`;
    console.log(message);
    logHistory.unshift(formattedMessage);
    if (logHistory.length > MAX_LOG_LINES) {
        logHistory.pop();
    }
    ui.run(function() {
        floatingWindow.log_text.setText(logHistory.join('\n'));
    });
}
// =================================================================


// --- 子线程：弹窗订单处理 ---
function handlePopupOrders() {
    showLog("弹窗监控线程已启动...");
    while (true) {
        try {
            let noGrabButton = textStartsWith("不抢").findOne(200);
            if (noGrabButton && text("如选择不接单不会对您产生任何影响").exists()) {
                showLog("检测到弹窗，点击“不抢”");
                noGrabButton.click();
                sleep(1500);
            }
        } catch(e) { console.error("弹窗监控线程出错: ", e); sleep(1000); }
        sleep(250);
    }
}

// --- 主程序 ---
function main() {
    showLog("主脚本启动");
    showLog(`配置: 接驾<${config.maxDistanceToPickup}km, 收入>${config.minIncome}元`);

    while (true) {
        try {
            if (!id("com.sdu.didi.gsui:id/title_bar_name").text("抢单大厅").exists()) {
                showLog("未在抢单大厅，暂停5秒...");
                sleep(5000);
                continue; 
            }

            showLog("--- 开始新一轮刷新 ---");
            pullDownToRefresh();
            sleep(config.refreshWaitTime);

            let grabButtons = text("抢单").find();

            if (grabButtons.empty()) {
                if(text("没有更多了～").exists()){
                    showLog("当前无订单，延长等待...");
                    sleep(random(5000, 10000));
                } else {
                    showLog("未发现新订单");
                }
            } else {
                showLog(`发现 ${grabButtons.length} 个订单，开始分析...`);
                for (let i = 0; i < grabButtons.length; i++) {
                    let btn = grabButtons[i];
                    if(!btn) continue;
                    let orderCard = findOrderCard(btn);
                    if (!orderCard) continue;
                    let orderInfo = parseOrderInfo(orderCard);
                    if (!orderInfo) continue;

                    showLog(`订单${i+1}: 距${orderInfo.distanceToPickup}km,￥${orderInfo.income}, ${orderInfo.pickupAddress.split('|')[0]}->${orderInfo.dropoffAddress.split('|')[0]}`);

                    if (checkOrder(orderInfo)) {
                        showLog("✅ 好单！准备抢夺...");
                        simulateClick(btn);
                        showLog("已点击！等待结果...");
                        sleep(config.postClickWaitTime);

                        let failureButton = textMatches(/(返回大厅|继续抢单|确定|我知道了)/).findOne(500);
                        if (failureButton) {
                            showLog("订单被抢，返回大厅");
                            failureButton.click();
                            sleep(1000);
                        } 
                        else if (!id("com.sdu.didi.gsui:id/title_bar_name").text("抢单大厅").exists()) {
                            showLog("🎉🎉🎉 抢单成功！🎉🎉🎉");
                            playNotificationSound();
                            exit(); 
                        } 
                        else { showLog("状态未知，继续刷新"); }
                        break; 
                    }
                }
            }
        } catch (e) { console.error("主线程出错: ", e); }
        
        let randomInterval = random(config.refreshIntervalMin, config.refreshIntervalMax);
        showLog(`等待 ${Math.round(randomInterval / 100) / 10} 秒...`);
        sleep(randomInterval);
    }
}


// --- 核心辅助函数 ---
function playNotificationSound() {
    try {
        let RingtoneManager = android.media.RingtoneManager;
        let notificationUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
        let r = RingtoneManager.getRingtone(context, notificationUri);
        r.play();
        device.vibrate(1500);
        sleep(2000); 
    } catch(e) { device.vibrate(1500); }
}

function simulateClick(widget) {
    let bounds = widget.bounds();
    if (!bounds) { widget.click(); return; }
    let randomX = bounds.left + Math.round(bounds.width() * 0.15) + random(0, Math.round(bounds.width() * 0.7));
    let randomY = bounds.top + Math.round(bounds.height() * 0.15) + random(0, Math.round(bounds.height() * 0.7));
    let pressDuration = random(80, 150);
    press(randomX, randomY, pressDuration);
}

function findOrderCard(buttonWidget) {
    let parent = buttonWidget.parent();
    if (parent) { return parent.parent(); }
    return null;
}

function parseOrderInfo(orderCard) {
    try {
        const distanceRegex = /距您([\d.]+)km/;
        const incomeRegex = /(?:预估收入|价格约)([\d.]+)元/;
        const totalDistanceRegex = /预计全程([\d.]+)km/;
        let distanceWidget = orderCard.findOne(textMatches(distanceRegex));
        let incomeWidget = orderCard.findOne(textMatches(incomeRegex));
        let totalDistanceWidget = orderCard.findOne(textMatches(totalDistanceRegex));
        let distanceToPickup = distanceWidget ? parseFloat(distanceWidget.text().match(distanceRegex)[1]) : -1;
        let income = incomeWidget ? parseFloat(incomeWidget.text().match(incomeRegex)[1]) : -1;
        let totalDistance = totalDistanceWidget ? parseFloat(totalDistanceWidget.text().match(totalDistanceRegex)[1]) : -1;
        let addressWidgets = orderCard.find(textContains("|"));
        let pickupAddress = addressWidgets.length > 0 ? addressWidgets[0].text() : "未知起点";
        let dropoffAddress = addressWidgets.length > 1 ? addressWidgets[1].text() : "未知终点";
        let logText = `接驾 ${distanceToPickup}km, 收入 ${income}元, 全程 ${totalDistance}km, [${pickupAddress}] -> [${dropoffAddress}]`;
        return {
            distanceToPickup, income, totalDistance, pickupAddress, dropoffAddress, logText
        };
    } catch (e) { return null; }
}

function checkOrder(orderInfo) {
    if (orderInfo.distanceToPickup === -1 || orderInfo.income === -1) return false;
    if (orderInfo.distanceToPickup > config.maxDistanceToPickup) return false;
    if (orderInfo.income < config.minIncome) return false;
    if (orderInfo.totalDistance < config.minTotalDistance) return false;
    if (config.pickupKeywords.length > 0 && !config.pickupKeywords.some(keyword => orderInfo.pickupAddress.includes(keyword))) return false;
    if (config.dropoffKeywords.length > 0 && !config.dropoffKeywords.some(keyword => orderInfo.dropoffAddress.includes(keyword))) return false;
    return true;
}

function pullDownToRefresh() {
    let dw = device.width; let dh = device.height; let duration = 300 + random(0, 100);
    let xStart = dw / 2 + random(-50, 50); let yStart = dh * 0.3;
    let xEnd = dw / 2 + random(-50, 50); let yEnd = dh * 0.7;
    let startPoint = [xStart, yStart]; let endPoint = [xEnd, yEnd];
    let ctl1 = [startPoint[0] + random(-40, 40), startPoint[1] + (endPoint[1] - startPoint[1]) * 0.3];
    let ctl2 = [endPoint[0] + random(-40, 40), startPoint[1] + (endPoint[1] - startPoint[1]) * 0.7];
    let path = getBezierPath(endPoint, ctl1, ctl2, startPoint, 100);
    let params = [parseInt(duration)];
    path.forEach(point => { params.push([parseInt(point.x), parseInt(point.y)]); });
    gesture.apply(null, params);
}

function getBezierPath(p1, p2, p3, p4, times) {
    function Point2D(x, y) { this.x = x || 0.0; this.y = y || 0.0; }
    function PointOnCubicBezier(cp, t) {
        var ax, bx, cx; var ay, by, cy; var tSquared, tCubed;
        var result = new Point2D();
        cx = 3.0 * (cp[1].x - cp[0].x); bx = 3.0 * (cp[2].x - cp[1].x) - cx; ax = cp[3].x - cp[0].x - cx - bx;
        cy = 3.0 * (cp[1].y - cp[0].y); by = 3.0 * (cp[2].y - cp[1].y) - cy; ay = cp[3].y - cp[0].y - cy - by;
        tSquared = t * t; tCubed = tSquared * t;
        result.x = ax * tCubed + bx * tSquared + cx * t + cp[0].x;
        result.y = ay * tCubed + by * tSquared + cy * t + cp[0].y;
        return result;
    }
    function ComputeBezier(cp, numberOfPoints, curve) {
        var dt; var i; dt = 1.0 / (numberOfPoints - 1);
        for (i = 0; i < numberOfPoints; i++) { curve[i] = PointOnCubicBezier(cp, i * dt); }
    }
    var cp = [ new Point2D(parseInt(p4[0]), parseInt(p4[1])), new Point2D(p2[0], p2[1]), new Point2D(p3[0], p3[1]), new Point2D(p1[0], p1[1]), ];
    var curve = [];
    ComputeBezier(cp, times, curve);
    return curve;
}


// --- 脚本启动入口 ---
auto.waitFor();
// 启动子线程来处理弹窗
threads.start(handlePopupOrders);
// 运行主线程的抢单逻辑
main();