"ui";

var color = "#009688";

ui.layout(
    <drawer id="drawer">
        <vertical>
            <appbar>
                <toolbar id="toolbar" title="九凤"/>
                <tabs id="tabs"/>
            </appbar>
            <viewpager id="viewpager">
                <frame>
                    <ScrollView>
                        <vertical padding="16">
                            <card cardCornerRadius="8dp" cardElevation="4dp" margin="8">
                                <vertical padding="16">
                                    <horizontal gravity="center_vertical">
                                        <text text="大厅开关" textSize="16sp" textColor="black" layout_weight="1"/>
                                        <Switch id="hallOrderSwitch" checked="true"/>
                                    </horizontal>
                                </vertical>
                            </card>

                            <card cardCornerRadius="8dp" cardElevation="4dp" margin="8">
                                <vertical padding="16">
                                    <text text="订单类型" textSize="16sp" textColor="black" marginBottom="8"/>
                                    <checkbox id="quickOrderCheck" text="快车" checked="true"/>
                                    <checkbox id="discountQuickCheck" text="特惠快车" checked="true"/>
                                    <checkbox id="didiExpressCheck" text="滴滴特快" checked="false"/>
                                    <checkbox id="customOrderCheck" text="自选单" checked="false"/>
                                    <checkbox id="realtimeOrderCheck" text="随心接实时单" checked="false"/>
                                    <checkbox id="allOrderCheck" text="所有订单" checked="false"/>
                                </vertical>
                            </card>

                            <card cardCornerRadius="8dp" cardElevation="4dp" margin="8">
                                <vertical padding="16">
                                    <horizontal gravity="center_vertical" marginBottom="8">
                                        <text text="订单金额" textSize="16sp" textColor="black" layout_weight="1"/>
                                        <Switch id="amountSwitch" checked="false"/>
                                    </horizontal>
                                    <horizontal gravity="center_vertical" id="amountInputLayout">
                                        <text text="最低金额:" textSize="14sp" textColor="gray"/>
                                        <input id="minAmountInput" hint="0" inputType="numberDecimal" layout_weight="1" margin="4"/>
                                        <text text="最高金额:" textSize="14sp" textColor="gray"/>
                                        <input id="maxAmountInput" hint="999" inputType="numberDecimal" layout_weight="1" margin="4"/>
                                    </horizontal>
                                </vertical>
                            </card>

                            <card cardCornerRadius="8dp" cardElevation="4dp" margin="8">
                                <vertical padding="16">
                                    <horizontal gravity="center_vertical" marginBottom="8">
                                        <text text="订单起点" textSize="16sp" textColor="black" layout_weight="1"/>
                                        <Switch id="startDistanceSwitch" checked="false"/>
                                    </horizontal>
                                    <horizontal gravity="center_vertical" id="startDistanceInputLayout">
                                        <text text="小于:" textSize="14sp" textColor="gray"/>
                                        <input id="maxStartDistanceInput" hint="10" inputType="numberDecimal" layout_weight="1" margin="4"/>
                                        <text text="公里" textSize="14sp" textColor="gray"/>
                                    </horizontal>
                                </vertical>
                            </card>

                            <card cardCornerRadius="8dp" cardElevation="4dp" margin="8">
                                <vertical padding="16">
                                    <horizontal gravity="center_vertical" marginBottom="8">
                                        <text text="订单全程" textSize="16sp" textColor="black" layout_weight="1"/>
                                        <Switch id="totalDistanceSwitch" checked="false"/>
                                    </horizontal>
                                    <horizontal gravity="center_vertical" id="totalDistanceInputLayout">
                                        <text text="大于:" textSize="14sp" textColor="gray"/>
                                        <input id="minTotalDistanceInput" hint="5" inputType="numberDecimal" layout_weight="1" margin="4"/>
                                        <text text="公里" textSize="14sp" textColor="gray"/>
                                    </horizontal>
                                </vertical>
                            </card>

                            <card cardCornerRadius="8dp" cardElevation="4dp" margin="8">
                                <vertical padding="16">
                                    <horizontal gravity="center_vertical" marginBottom="8">
                                        <text text="订单单价" textSize="16sp" textColor="black" layout_weight="1"/>
                                        <Switch id="unitPriceSwitch" checked="false"/>
                                    </horizontal>
                                    <horizontal gravity="center_vertical" id="unitPriceInputLayout">
                                        <text text="最低单价:" textSize="14sp" textColor="gray"/>
                                        <input id="minUnitPriceInput" hint="2.0" inputType="numberDecimal" layout_weight="1" margin="4"/>
                                        <text text="元/公里" textSize="14sp" textColor="gray"/>
                                    </horizontal>
                                </vertical>
                            </card>

                            <card cardCornerRadius="8dp" cardElevation="4dp" margin="8">
                                <vertical padding="16">
                                    <horizontal gravity="center_vertical" marginBottom="8">
                                        <text text="大单必抢" textSize="16sp" textColor="black" layout_weight="1"/>
                                        <Switch id="bigOrderSwitch" checked="false"/>
                                    </horizontal>
                                    <horizontal gravity="center_vertical" id="bigOrderInputLayout">
                                        <text text="大单金额:" textSize="14sp" textColor="gray"/>
                                        <input id="bigOrderAmountInput" hint="100" inputType="numberDecimal" layout_weight="1" margin="4"/>
                                        <text text="元" textSize="14sp" textColor="gray"/>
                                    </horizontal>
                                </vertical>
                            </card>

                            <card cardCornerRadius="8dp" cardElevation="4dp" margin="8">
                                <vertical padding="16">
                                    <text text="刷新间隔" textSize="16sp" textColor="black" marginBottom="8"/>
                                    <horizontal gravity="center_vertical">
                                        <text text="最小值:" textSize="14sp" textColor="gray"/>
                                        <input id="refreshMinInput" text="500" inputType="number" layout_weight="1" margin="4"/>
                                        <text text="最大值:" textSize="14sp" textColor="gray"/>
                                        <input id="refreshMaxInput" text="1000" inputType="number" layout_weight="1" margin="4"/>
                                        <text text="毫秒" textSize="14sp" textColor="gray"/>
                                    </horizontal>
                                </vertical>
                            </card>

                            <button id="saveConfigBtn" text="保存设置" textColor="white" bg="#009688" margin="16" textSize="16sp"/>
                        </vertical>
                    </ScrollView>
                </frame>
                <frame>
                    <text text="弹窗订单" textColor="red" textSize="16sp"/>
                </frame>
                <frame>
                    <text text="卡密" textColor="green" textSize="16sp"/>
                </frame>
            </viewpager>
        </vertical>
        <vertical layout_gravity="left" bg="#ffffff" w="280">
            <img w="280" h="200" scaleType="fitXY" src="http://images.shejidaren.com/wp-content/uploads/2014/10/023746fki.jpg"/>
            <list id="menu">
                <horizontal bg="?selectableItemBackground" w="*">
                    <img w="50" h="50" padding="16" src="{{this.icon}}" tint="{{color}}"/>
                    <text textColor="black" textSize="15sp" text="{{this.title}}" layout_gravity="center"/>
                </horizontal>
            </list>
        </vertical>
    </drawer>
);

// 配置存储
var storage = storages.create('hall_order_config');

// 默认配置
function getDefaultConfig() {
    return {
        hallOrderEnabled: true,
        orderTypes: ['快车', '特惠快车'],
        amountEnabled: false,
        minAmount: 0,
        maxAmount: 999,
        startDistanceEnabled: false,
        maxStartDistance: 10,
        totalDistanceEnabled: false,
        minTotalDistance: 5,
        unitPriceEnabled: false,
        minUnitPrice: 2.0,
        bigOrderEnabled: false,
        bigOrderAmount: 100,
        refreshIntervalMin: 500,
        refreshIntervalMax: 1000
    };
}

// 加载配置
function loadConfig() {
    var config = storage.get('config', getDefaultConfig());
    
    // 设置开关状态
    ui.hallOrderSwitch.setChecked(config.hallOrderEnabled);
    ui.amountSwitch.setChecked(config.amountEnabled);
    ui.startDistanceSwitch.setChecked(config.startDistanceEnabled);
    ui.totalDistanceSwitch.setChecked(config.totalDistanceEnabled);
    ui.unitPriceSwitch.setChecked(config.unitPriceEnabled);
    ui.bigOrderSwitch.setChecked(config.bigOrderEnabled);
    
    // 设置订单类型多选框
    ui.quickOrderCheck.setChecked(config.orderTypes.includes('快车'));
    ui.discountQuickCheck.setChecked(config.orderTypes.includes('特惠快车'));
    ui.didiExpressCheck.setChecked(config.orderTypes.includes('滴滴特快'));
    ui.customOrderCheck.setChecked(config.orderTypes.includes('自选单'));
    ui.realtimeOrderCheck.setChecked(config.orderTypes.includes('随心接实时单'));
    ui.allOrderCheck.setChecked(config.orderTypes.includes('所有订单'));
    
    // 设置输入框值
    ui.minAmountInput.setText(config.minAmount.toString());
    ui.maxAmountInput.setText(config.maxAmount.toString());
    ui.maxStartDistanceInput.setText(config.maxStartDistance.toString());
    ui.minTotalDistanceInput.setText(config.minTotalDistance.toString());
    ui.minUnitPriceInput.setText(config.minUnitPrice.toString());
    ui.bigOrderAmountInput.setText(config.bigOrderAmount.toString());
    ui.refreshMinInput.setText(config.refreshIntervalMin.toString());
    ui.refreshMaxInput.setText(config.refreshIntervalMax.toString());
    
    // 更新输入框状态
    updateInputState();
    
    return config;
}

// 保存配置
function saveConfig() {
    try {
        var config = {
            hallOrderEnabled: ui.hallOrderSwitch.isChecked(),
            orderTypes: getSelectedOrderTypes(),
            amountEnabled: ui.amountSwitch.isChecked(),
            minAmount: parseFloat(ui.minAmountInput.getText()) || 0,
            maxAmount: parseFloat(ui.maxAmountInput.getText()) || 999,
            startDistanceEnabled: ui.startDistanceSwitch.isChecked(),
            maxStartDistance: parseFloat(ui.maxStartDistanceInput.getText()) || 10,
            totalDistanceEnabled: ui.totalDistanceSwitch.isChecked(),
            minTotalDistance: parseFloat(ui.minTotalDistanceInput.getText()) || 5,
            unitPriceEnabled: ui.unitPriceSwitch.isChecked(),
            minUnitPrice: parseFloat(ui.minUnitPriceInput.getText()) || 2.0,
            bigOrderEnabled: ui.bigOrderSwitch.isChecked(),
            bigOrderAmount: parseFloat(ui.bigOrderAmountInput.getText()) || 100,
            refreshIntervalMin: parseInt(ui.refreshMinInput.getText()) || 500,
            refreshIntervalMax: parseInt(ui.refreshMaxInput.getText()) || 1000
        };
        
        // 验证配置
        if (!validateConfig(config)) {
            return false;
        }
        
        storage.put('config', config);
        toast('配置保存成功！');
        return true;
    } catch (e) {
        toast('保存配置失败：' + e.message);
        return false;
    }
}

// 获取选中的订单类型
function getSelectedOrderTypes() {
    var types = [];
    if (ui.quickOrderCheck.isChecked()) types.push('快车');
    if (ui.discountQuickCheck.isChecked()) types.push('特惠快车');
    if (ui.didiExpressCheck.isChecked()) types.push('滴滴特快');
    if (ui.customOrderCheck.isChecked()) types.push('自选单');
    if (ui.realtimeOrderCheck.isChecked()) types.push('随心接实时单');
    if (ui.allOrderCheck.isChecked()) types.push('所有订单');
    return types;
}

// 验证配置
function validateConfig(config) {
    // 验证金额设置
    if (config.amountEnabled) {
        if (config.minAmount < 0 || config.maxAmount < 0) {
            toast('金额不能为负数');
            return false;
        }
        if (config.minAmount > config.maxAmount) {
            toast('最低金额不能大于最高金额');
            return false;
        }
    }

    // 验证距离设置
    if (config.startDistanceEnabled && config.maxStartDistance <= 0) {
        toast('起点距离必须大于0');
        return false;
    }

    if (config.totalDistanceEnabled && config.minTotalDistance <= 0) {
        toast('全程距离必须大于0');
        return false;
    }

    // 验证单价设置
    if (config.unitPriceEnabled && config.minUnitPrice <= 0) {
        toast('单价必须大于0');
        return false;
    }

    // 验证大单设置
    if (config.bigOrderEnabled && config.bigOrderAmount <= 0) {
        toast('大单金额必须大于0');
        return false;
    }

    // 验证刷新间隔
    if (config.refreshIntervalMin <= 0 || config.refreshIntervalMax <= 0) {
        toast('刷新间隔必须大于0');
        return false;
    }

    if (config.refreshIntervalMin > config.refreshIntervalMax) {
        toast('刷新间隔最小值不能大于最大值');
        return false;
    }

    // 验证订单类型
    if (config.orderTypes.length === 0) {
        toast('请至少选择一种订单类型');
        return false;
    }

    return true;
}

// 更新输入框状态
function updateInputState() {
    // 金额输入框状态
    var amountEnabled = ui.amountSwitch.isChecked();
    ui.minAmountInput.setEnabled(amountEnabled);
    ui.maxAmountInput.setEnabled(amountEnabled);

    // 起点距离输入框状态
    var startDistanceEnabled = ui.startDistanceSwitch.isChecked();
    ui.maxStartDistanceInput.setEnabled(startDistanceEnabled);

    // 全程距离输入框状态
    var totalDistanceEnabled = ui.totalDistanceSwitch.isChecked();
    ui.minTotalDistanceInput.setEnabled(totalDistanceEnabled);

    // 单价输入框状态
    var unitPriceEnabled = ui.unitPriceSwitch.isChecked();
    ui.minUnitPriceInput.setEnabled(unitPriceEnabled);

    // 大单输入框状态
    var bigOrderEnabled = ui.bigOrderSwitch.isChecked();
    ui.bigOrderAmountInput.setEnabled(bigOrderEnabled);
}

// 读取配置用于业务处理
function getConfig() {
    return storage.get('config', getDefaultConfig());
}

// 事件绑定
ui.run(function() {
    // 加载配置
    loadConfig();

    // 开关变化事件
    ui.amountSwitch.on('check', function() {
        updateInputState();
    });

    ui.startDistanceSwitch.on('check', function() {
        updateInputState();
    });

    ui.totalDistanceSwitch.on('check', function() {
        updateInputState();
    });

    ui.unitPriceSwitch.on('check', function() {
        updateInputState();
    });

    ui.bigOrderSwitch.on('check', function() {
        updateInputState();
    });

    // 保存按钮点击事件
    ui.saveConfigBtn.on('click', function() {
        saveConfig();
    });
});

//创建选项菜单(右上角)
ui.emitter.on("create_options_menu", menu=>{
    menu.add("设置");
    menu.add("关于");
});

//监听选项菜单点击
ui.emitter.on("options_item_selected", (e, item)=>{
    switch(item.getTitle()){
        case "设置":
            toast("还没有设置");
            break;
        case "关于":
            alert("关于", "Auto.js界面模板 v1.0.0");
            break;
    }
    e.consumed = true;
});

activity.setSupportActionBar(ui.toolbar);

//设置滑动页面的标题
ui.viewpager.setTitles(["特惠抢单大厅", "快车抢单大厅", "通用设置"]);
//让滑动页面和标签栏联动
ui.tabs.setupWithViewPager(ui.viewpager);

//让工具栏左上角可以打开侧拉菜单
ui.toolbar.setupWithDrawer(ui.drawer);

ui.menu.setDataSource([
  {
      title: "选项一",
      icon: "@drawable/ic_android_black_48dp"
  },
  {
      title: "选项二",
      icon: "@drawable/ic_settings_black_48dp"
  },
  {
      title: "选项三",
      icon: "@drawable/ic_favorite_black_48dp"
  },
  {
      title: "退出",
      icon: "@drawable/ic_exit_to_app_black_48dp"
  }
]);

ui.menu.on("item_click", item => {
    switch(item.title){
        case "退出":
            ui.finish();
            break;
    }
});
